package com.rs.module.acp.controller.admin.zh;

import com.rs.module.acp.controller.admin.zh.vo.warningeducation.WarningEducationRespVO;
import com.rs.module.acp.controller.admin.zh.vo.warningeducation.WarningEducationSaveReqVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.zh.WarningEducationDO;
import com.rs.module.acp.service.zh.WarningEducationService;

@Api(tags = "综合管理-警示教育")
@RestController
@RequestMapping("/acp/zh/warningEducation")
@Validated
public class WarningEducationController {

    @Resource
    private WarningEducationService warningEducationService;

    @GetMapping("/delete")
    @ApiOperation(value = "删除综合管理-警示教育")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteWarningEducation(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           warningEducationService.deleteWarningEducation(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得综合管理-警示教育")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<WarningEducationRespVO> getWarningEducation(@RequestParam("id") String id) {
        WarningEducationDO warningEducation = warningEducationService.getWarningEducation(id);
        return success(BeanUtils.toBean(warningEducation, WarningEducationRespVO.class));
    }
    @PostMapping("/save")
    @ApiOperation(value = "创建/编辑综合管理-警示教育")
    public CommonResult<String> save(@Valid @RequestBody WarningEducationSaveReqVO createReqVO) {
        try {
            return success(warningEducationService.save(createReqVO));
        } catch (Exception e) {
            return CommonResult.error(e.getMessage());
        }
    }
}
