package com.rs.module.acp.controller.admin.zh.vo.warningeducation;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 综合管理-警示教育关联人员新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class WarningEducationPersonSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("警示教育ID")
    private String warningEducationId;

    @ApiModelProperty("警示人（民警）身份证号")
    @NotEmpty(message = "警示人（民警）身份证号不能为空")
    private String warningPoliceSfzh;

    @ApiModelProperty("警示人（民警）姓名")
    @NotEmpty(message = "警示人（民警）姓名不能为空")
    private String warningPoliceName;

}
