<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.base.dao.pm.PrisonerListDao">


    <select id="getCivilizedPersonalPrisonerSelectCompomentList"
            resultType="com.rs.module.base.entity.pm.PrisonerListDO">
        select e.* from(
        select a.*, coalesce(b.violation_num,0) as bywgs, coalesce(c.punishment_num,0) as bycfs,
        coalesce(d.reward_num,0) as byjls, (coalesce(b.violation_num,0)+coalesce(c.punishment_num,0)) as bywgcfs from
        (select * from vw_acp_pm_prisoner_List where is_del = 0
        <if test="startAddTime!=null">
            and add_time &gt;= #{startAddTime}
        </if>
        <if test="endAddTime!=null">
            and add_time &lt; #{endAddTime}
        </if>
        <if test="startRssj!=null">
            and rssj &gt;= #{startRssj}
        </if>
        <if test="endRssj!=null">
            and rssj &lt; #{endRssj}
        </if>
        <if test="req.orgCode!=null and req.orgCode!=''">
            and org_code =#{req.orgCode}
        </if>
        <if test="req.areaId!=null and req.areaId!=''">
            and area_id =#{req.areaId}
        </if>
        <if test="req.jsh!=null and req.jsh!=''">
            and jsh = #{req.jsh}
        </if>
        <if test="req.bjgrylx!=null and req.bjgrylx!=''">
            and bjgrylx = #{req.bjgrylx}
        </if>
        <if test="ryzt!=null and ryzt!=''">
            and ryzt = #{ryzt}
        </if>
        <if test="xmPy!=null and xmPy!=''">
            and (
            xm like concat('%',#{xmPy},'%')
            or xmpy like concat('%',#{xmPy},'%')
            or bm like concat('%',#{xmPy},'%')
            )
        </if>

        ) a
        left join
        (select jgrybm, count(1) as violation_num from acp_pi_violation_record where is_del = 0 and add_time BETWEEN
        date_trunc('month', now()) and date_trunc('month', now()+ INTERVAL ' 1 month') GROUP BY jgrybm) b
        on a.jgrybm = b.jgrybm
        left join
        (select jgrybm, count(1) as punishment_num from acp_gj_punishment where is_del = 0 and status in
        ('03','05','06') and sfyg = '0' and (actual_end_date is null or actual_end_date >= date_trunc('month', now()))
        GROUP BY jgrybm) c
        on a.jgrybm = c.jgrybm
        left join
        (select jgrybm, count(1) as reward_num from acp_gj_reward where is_del = 0 and status = '03' and execution_time
        BETWEEN date_trunc('month', now()) and date_trunc('month', now()+ INTERVAL ' 1 month') GROUP BY jgrybm) d
        on a.jgrybm = d.jgrybm
        ) e where 1=1 order by e.bywgcfs asc, e.byjls desc
    </select>

    <select id="getForeinPersionnelSelectCompoment"
            resultType="com.rs.module.base.controller.admin.pm.vo.ForeignPersonnelListRespVO">
        WITH RECURSIVE cte AS (
        SELECT "id",add_time,org_code,jh,xm,zjlx,zjhm,xb,zp_url,'01' AS ry_type FROM acp_wb_case_personnel WHERE is_del
        = '0' AND org_code = #{pageReqVO.orgCode} UNION
        SELECT "id",add_time,org_code,jh,xm,zjlx,zjhm,xb,zp_url,'01' AS ry_type FROM acp_wb_coop_case_personnel WHERE
        is_del = '0' AND org_code = #{pageReqVO.orgCode} UNION
        SELECT "id",add_time,org_code,NULL::text AS jh,xm,zjlx,zjhm,xb,zp_url,'02' AS ry_type FROM acp_wb_lawyer WHERE
        is_del = '0' AND org_code = #{pageReqVO.orgCode} UNION
        SELECT "id",add_time,org_code,NULL::text AS jh,name AS xm,id_type AS zjlx, id_number AS zjhm, gender AS
        xb,image_url AS zp_url,'03' AS ry_type FROM acp_wb_social_relations WHERE is_del = '0' AND org_code = #{pageReqVO.orgCode} UNION
        SELECT "id",add_time,org_code,NULL::text AS jh,name AS xm,id_type AS zjlx, id_number AS zjhm, gender AS
        xb,image_url AS zp_url,'04' AS ry_type FROM acp_wb_consular WHERE is_del = '0' AND org_code = #{pageReqVO.orgCode} UNION
        SELECT "id",add_time,org_code,NULL::text AS jh,name AS xm,id_type AS zjlx, id_number AS zjhm, NULL::text AS
        xb,NULL::text AS zp_url,'05' AS ry_type FROM acp_gj_visitor_info WHERE is_del = '0' AND org_code = #{pageReqVO.orgCode})
        SELECT * FROM cte
        <where>
            <if test="pageReqVO.xm != null and pageReqVO.xm != ''">
                AND xm like concat('%',#{pageReqVO.xm},'%')
            </if>
            <if test="pageReqVO.ryType != null and pageReqVO.ryType != ''">
                AND ry_type = #{pageReqVO.ryType}
            </if>
            <if test="pageReqVO.addTime != null and pageReqVO.addTime.length > 0">
                AND add_time BETWEEN #{pageReqVO.addTime[0]} AND #{pageReqVO.addTime[1]}
            </if>
        </where>
    </select>

</mapper>
